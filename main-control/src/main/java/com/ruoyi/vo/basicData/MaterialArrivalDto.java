package com.ruoyi.vo.basicData;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;

/**
 * 物料到货确认DTO（通用）
 * 入库场景：物料来料确认
 * 出库场景：物料备货确认
 *
 * <AUTHOR>
 */
@Data
public class MaterialArrivalDto {
    
    /**
     * 单据明细ID
     */
    @NotBlank(message = "单据明细ID不能为空")
    private String detailId;

    /**
     * 物料编码
     */
    @NotBlank(message = "物料编码不能为空")
    private String materialCode;

    /**
     * 到货数量（通用字段）
     * 入库场景：来料数量（当前批次实际到货数量）
     * 出库场景：备货数量（当前批次准备出库数量）
     */
    @NotNull(message = "到货数量不能为空")
    @Positive(message = "到货数量必须大于0")
    private Integer arrivalQuantity;
    
    /**
     * 备注
     */
    private String remark;
}
