package com.ruoyi.utils.constant;

/**
 * 单据状态常量类
 * 
 * <AUTHOR>
 */
public class DocumentStatusConstant {

    /**
     * 单据明细任务状态
     */
    public static class TaskStatus {
        /** 待处理（入库为待入库，出库为待出库） */
        public static final Integer PENDING = 0;
        /** 处理中（入库为部分入库，出库为部分出库） */
        public static final Integer PROCESSING = 1;
        /** 已完成（入库为入库完成，出库为出库完成） */
        public static final Integer COMPLETED = 2;
    }



    /**
     * 批次处理状态
     */
    public static class BatchStatus {
        /** 待质检（需要质检的批次初始状态） */
        public static final Integer PENDING_QC = 1;
        /** 质检中（质检任务进行中） */
        public static final Integer QC_IN_PROGRESS = 2;
        /** 待处理（质检完成或无需质检，入库为待入库，出库为待出库） */
        public static final Integer PENDING_PROCESS = 3;
        /** 已处理（入库为已入库，出库为已出库） */
        public static final Integer PROCESSED = 4;
    }

    /**
     * 状态描述获取方法
     */
    public static class StatusDescription {
        
        /**
         * 获取任务状态描述
         * @param status 状态值
         * @param isInbound 是否为入库单据
         * @return 状态描述
         */
        public static String getTaskStatusDesc(Integer status, boolean isInbound) {
            if (status == null) return "未知状态";
            
            switch (status) {
                case 0:
                    return isInbound ? "待入库" : "待出库";
                case 1:
                    return isInbound ? "部分入库" : "部分出库";
                case 2:
                    return isInbound ? "入库完成" : "出库完成";
                default:
                    return "未知状态";
            }
        }

        /**
         * 获取批次状态描述
         * @param status 状态值
         * @param isInbound 是否为入库单据
         * @return 状态描述
         */
        public static String getBatchStatusDesc(Integer status, boolean isInbound) {
            if (status == null) return "未知状态";
            
            switch (status) {
                case 1:
                    return "待质检";
                case 2:
                    return "质检中";
                case 3:
                    return isInbound ? "待入库" : "待出库";
                case 4:
                    return isInbound ? "已入库" : "已出库";
                default:
                    return "未知状态";
            }
        }


    }
}
