package com.ruoyi.domain.basicData;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 *单据明细详情表
 */
@Data
@TableName("basic_document_detail")
public class BasicDocumentDetail {

    /**
     * 主键编码
     */
    private String id;
    /**
     * 父表单据编号
     */
    private String documentCode;

    /**
     * 物料编码
     */
    private String materialCode;
    /**
     * 计划数量（单据要求的目标数量）
     * 入库场景：采购数量、生产入库计划数量
     * 出库场景：销售出库数量、生产领料数量
     */
    private Integer quantity;

    /**
     * 已完成数量（最终完成处理的数量）
     * 入库场景：已入库数量（质检合格并完成入库的数量）
     * 出库场景：已出库数量（实际已出库的数量）
     */
    private Integer completedNum;

    /**
     * 本次数量（已废弃字段，请使用对应VO类中的数量字段）
     * 单据出入库场景：使用 DocumentDetailVo.currentNum
     * 物料到货确认场景：使用 MaterialArrivalDto.arrivalQuantity
     * @deprecated 此字段已无实际用途
     */
    @Deprecated
    private Integer currentNum;

    /**
     * 总到货数量（累计所有批次的实际到货数量，包含不合格品）
     * 入库场景：总来料数量（所有批次累计到货，可能超过计划数量）
     * 出库场景：总准备出库数量（所有批次累计准备出库的数量）
     * 注：此数量可能大于等于已完成数量，因为包含不合格或未通过处理的部分
     */
    private Integer totalArrivalNum;
    /**
     * 任务状态：
     * 0-待处理（单据锁定后的初始状态，入库为待入库，出库为待出库）
     * 1-处理中（部分数量已处理，入库为部分入库，出库为部分出库）
     * 2-已完成（全部数量已处理完成，入库为入库完成，出库为出库完成）
     */
    private Integer taskStatus;
}