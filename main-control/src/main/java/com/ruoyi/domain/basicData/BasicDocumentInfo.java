package com.ruoyi.domain.basicData;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 基础单据信息
 */
@Data
@TableName("basic_document_info")
public class BasicDocumentInfo {

    /**
     * 主键编码
     */
    private String id;
    /**
     * 单据编号（WMS内部生成）
     */
    private String transactionCode;

    /**
     * 来源单据编号（ERP等外部系统的原始单据编号）
     */
    private String sourceDocumentNo;

    /**
     * 单据分类 1:出库 2:入库
     */
    private Integer transactionType;
    /**
     * 单据类型:1:生产领料 2:生产补料 3:生产入库（成品入库） 4:生产退料 5采购入库  6采购退货出库  7销售出库  8销售退货入库 9物料调拨 10库存盘点 11仓库用料
     */
    private Integer businessType;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date receTime;

    /**
     * 状态 0未结束，1已完成
     */
    private Integer status;


    /**
     * 是否锁定0未锁定，1已锁定
     */
    private Integer isLock;

    /**
     * 单据来源，0页面，1MES，2ERP
     */
    private Integer businessSource;

    /**
     * 供销编码
     */
    private String supplySalesCode;

    /**
     * 创建人
     */
    private String createName;

    /**
     * 供应商/客户名称（非数据库字段，用于查询结果展示）
     */
    @TableField(exist = false)
    private String supplierCustomerName;
}