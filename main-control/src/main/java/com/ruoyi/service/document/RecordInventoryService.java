package com.ruoyi.service.document;

import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.bean.BeanUtils;
import com.ruoyi.domain.basicData.BasicMaterialBatchInventory;
import com.ruoyi.domain.basicData.RecordMaterialInout;
import com.ruoyi.domain.document.RecordInventoryDetail;
import com.ruoyi.domain.document.RecordInventoryInfo;
import com.ruoyi.mapper.document.RecordInventoryDetailMapper;
import com.ruoyi.mapper.document.RecordInventoryInfoMapper;
import com.ruoyi.service.basicData.BasicMaterialBatchInventoryService;
import com.ruoyi.service.work.RecordMaterialInoutService;
import com.ruoyi.utils.AutoNum;
import com.ruoyi.utils.DateAndTimeUtil;
import com.ruoyi.utils.QueryParamVO;
import com.ruoyi.utils.ResponseResult;
import com.ruoyi.utils.constant.CommonConstant;
import com.ruoyi.vo.warehouse.RecordInventoryDetailDto;
import com.ruoyi.vo.warehouse.RecordInventoryDetailVo;
import com.ruoyi.vo.warehouse.RecordInventoryDto;
import com.ruoyi.vo.warehouse.RecordInventoryInfoVo;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.UUID;

/**
 * 库存盘点相关服务
 */

@Service
public class RecordInventoryService {

    @Resource
    RecordInventoryInfoMapper recordInventoryInfoMapper;
    @Resource
    RecordInventoryDetailMapper recordInventoryDetailMapper;
    @Resource
    BasicMaterialBatchInventoryService basicMaterialBatchInventoryService;
    @Resource
    RecordMaterialInoutService recordMaterialInoutService;

    /**
     *获取库存盘点单据编号
     */
    public String getMaxIndex(String str) {
        AutoNum an = new AutoNum();
        String strDate = an.getStrDate();
        String mxstr = this.recordInventoryInfoMapper.getMaxIndex(str + strDate + "%");
        if (StringUtils.isEmpty(mxstr)) {
            mxstr = str + DateAndTimeUtil.getNowTimeNoSeparator();
        }
        return an.getNum(str, mxstr);
    }

    /**
     *新增库存盘点
     */
    @Transactional
    public ResponseResult addInventoryRecord(RecordInventoryDto recordInventoryDto) {
        //判断是否存在冻结的物料
        Integer totalNum = 0;
        if (recordInventoryDto.getDetailList() != null && recordInventoryDto.getDetailList().size()>0 ){
            for (RecordInventoryDetailDto recordInventoryDetailDto : recordInventoryDto.getDetailList()){
                if (recordInventoryDetailDto.getFreezeNum() != 0){
                    return ResponseResult.getErrorResult("物料存在冻结物料，不允许发起盘点操作");
                }
                totalNum = totalNum + recordInventoryDetailDto.getMaterialNum();
            }
        }else {
            return ResponseResult.getErrorResult("盘点详情不能为空，请选择需要盘点的仓库物料");
        }
        RecordInventoryInfo recordInventoryInfo = new RecordInventoryInfo();
        BeanUtils.copyProperties(recordInventoryDto,recordInventoryInfo);
        recordInventoryInfo.setId(UUID.randomUUID().toString());
        String boundIndex = this.getMaxIndex(CommonConstant.CodePrefix.INVENTORY_PREFIX);
        recordInventoryInfo.setBoundIndex(boundIndex);
        recordInventoryInfo.setState(CommonConstant.BoundStatus.PENDING_RE);
        Date date = new Date();
        recordInventoryInfo.setRecordDate(date);
        recordInventoryInfo.setRecorder(SecurityUtils.getUsername());
        recordInventoryInfo.setTotalNum(totalNum);
        //保存物料库存盘点详情
        for (RecordInventoryDetailDto recordInventoryDetailDto : recordInventoryDto.getDetailList()) {
            RecordInventoryDetail recordInventoryDetail = new RecordInventoryDetail();
            BeanUtils.copyProperties(recordInventoryDetailDto, recordInventoryDetail);
            recordInventoryDetail.setId(UUID.randomUUID().toString());
            recordInventoryDetail.setBoundIndex(boundIndex);
            recordInventoryDetail.setRecordDate(date);
            recordInventoryDetailMapper.insert(recordInventoryDetail);
            //冻结批次库存
            BasicMaterialBatchInventory materialBatchInventory = basicMaterialBatchInventoryService.getMaterialBatchInventory(recordInventoryDetail.getContainerCode(), recordInventoryDetail.getBatch(), recordInventoryDetail.getMaterialCode(), recordInventoryDetail.getProduceDate());
            materialBatchInventory.setAvailNum(0);
            materialBatchInventory.setFreezeNum(recordInventoryDetail.getMaterialNum());
            basicMaterialBatchInventoryService.updateByPrimaryKeySelective(materialBatchInventory);
        }
        recordInventoryInfoMapper.insert(recordInventoryInfo);
        return ResponseResult.getSuccessResult();
    }

    /**
     *查询库存盘点
     */
    public List<RecordInventoryInfoVo> queryInventoryRecord(QueryParamVO queryParamVO) {
        return recordInventoryInfoMapper.queryInventoryRecord(queryParamVO);
    }
    /**
     *查询库存盘点详情
     */
    public List<RecordInventoryDetailVo> queryInventoryRecordDetail(QueryParamVO queryParamVO) {
        return recordInventoryDetailMapper.queryInventoryRecordDetail(queryParamVO);
    }

    /**
     *更新库存盘点
     */
    @Transactional
    public ResponseResult updateRecordInventoryInfo(RecordInventoryInfo param) {
        RecordInventoryInfo recordInventoryInfo = recordInventoryInfoMapper.selectById(param.getId());
        Integer currentState = recordInventoryInfo.getState();
        Integer targetState = param.getState();

        // 状态流转验证
        if (currentState.equals(CommonConstant.BoundStatus.LOCKED)){
            return ResponseResult.getErrorResult("单据已锁单不允许更改");
        }

        // 送审状态下禁止修改单据内容
        //todo 送审之后冻结数量要调整
        if (currentState.equals(CommonConstant.BoundStatus.UNDER_REVIEW)) {
            // 送审状态下只允许锁单操作，不允许修改其他内容
            if (targetState == null || !targetState.equals(CommonConstant.BoundStatus.LOCKED)) {
                return ResponseResult.getErrorResult("单据已送审，只能进行锁单操作，不允许修改其他内容");
            }
        }

        // 状态流转逻辑处理
        if (targetState != null) {
            // 送审操作：只能从未送审状态进行
            if (targetState.equals(CommonConstant.BoundStatus.UNDER_REVIEW)) {
                if (!currentState.equals(CommonConstant.BoundStatus.PENDING_RE)) {
                    return ResponseResult.getErrorResult("只能从未送审状态进行送审操作");
                }
                recordInventoryInfo.setState(CommonConstant.BoundStatus.UNDER_REVIEW);
            }
            // 锁单操作：只能从送审状态进行
            else if (targetState.equals(CommonConstant.BoundStatus.LOCKED)) {
                if (!currentState.equals(CommonConstant.BoundStatus.UNDER_REVIEW)) {
                    return ResponseResult.getErrorResult("只能从送审状态进行锁单操作");
                }
                recordInventoryInfo.setLockDate(new Date());
                recordInventoryInfo.setState(CommonConstant.BoundStatus.LOCKED);

                //更新相应容器实际库存，保存出入记录
                List<RecordInventoryDetail> recordInventoryDetails = recordInventoryDetailMapper.qryInventoryDetailByCode(recordInventoryInfo.getBoundIndex());
                Date date = new Date();
                String checker = SecurityUtils.getUsername();
                for (RecordInventoryDetail recordInventoryDetail : recordInventoryDetails){
                    //更新批次库存
                    BasicMaterialBatchInventory materialBatchInventory = basicMaterialBatchInventoryService.getMaterialBatchInventory(recordInventoryDetail.getContainerCode(), recordInventoryDetail.getBatch(), recordInventoryDetail.getMaterialCode(), recordInventoryDetail.getProduceDate());
                    materialBatchInventory.setMaterialNum(recordInventoryDetail.getInventoryNum());
                    materialBatchInventory.setAvailNum(recordInventoryDetail.getInventoryNum());
                    materialBatchInventory.setFreezeNum(0);
                    basicMaterialBatchInventoryService.updateByPrimaryKeySelective(materialBatchInventory);
                    //保存出入库记录
                    RecordMaterialInout recordMaterialInout = new RecordMaterialInout();
                    BeanUtils.copyProperties(recordInventoryDetail, recordMaterialInout);
                    recordMaterialInout.setId(UUID.randomUUID().toString());
                    if (recordInventoryDetail.getInventoryNum()>recordInventoryDetail.getMaterialNum()){
                        recordMaterialInout.setInoutType(CommonConstant.InoutType.IN);
                    }else {
                        recordMaterialInout.setInoutType(CommonConstant.InoutType.OUT);
                    }
                    recordMaterialInout.setUpperIndex(recordInventoryInfo.getBoundIndex());
                    recordMaterialInout.setRecordDate(new Date());
                    recordMaterialInout.setDataOrigin(CommonConstant.BusinessSource.WEB);
                    recordMaterialInout.setRecorder(checker);
                    recordMaterialInout.setLockTime(date);
                    recordMaterialInout.setBoundType(CommonConstant.BoundType.KCPD);
                    //取盘点数和原物料数绝对值
                    recordMaterialInout.setTotalNum(Math.abs(recordInventoryDetail.getMaterialNum() - recordInventoryDetail.getInventoryNum()));
                    recordMaterialInoutService.insertByRecordInoutDetail(recordMaterialInout);
                }
            }
            else {
                return ResponseResult.getErrorResult("不支持的状态流转操作");
            }
        }

        // 只有在未送审状态下才允许修改备注等内容
        if (currentState.equals(CommonConstant.BoundStatus.PENDING_RE) && targetState == null) {
            if (StringUtils.isNotEmpty(param.getRemark())){
                recordInventoryInfo.setRemark(param.getRemark());
            }
        }

        recordInventoryInfoMapper.updateById(recordInventoryInfo);
        return ResponseResult.getSuccessResult();
    }

    /**
     *更新库存盘点详情
     */
    @Transactional
    public ResponseResult updateRecordInventoryDetail(List<RecordInventoryDetail> param) {
        if (param != null && param.size()>0){
            for (RecordInventoryDetail recordInventoryDetail : param){
                //更新未锁单的盘点详情数据
                recordInventoryDetail.setRecordDate(new Date());
                recordInventoryDetailMapper.uptUnLockInventoryDetail(recordInventoryDetail);
            }
            return ResponseResult.getSuccessResult();
        }else {
            return ResponseResult.getErrorResult("盘点详情数据为空，请勾选需要更新的数据");
        }
    }
    /**
     *删除库存盘点
     */
    @Transactional
    public ResponseResult deleteRecordInventoryInfo(RecordInventoryInfo param) {
        RecordInventoryInfo recordInventoryInfo = recordInventoryInfoMapper.selectById(param.getId());
        if (recordInventoryInfo.getState() == CommonConstant.BoundStatus.LOCKED){
            return ResponseResult.getErrorResult("单据已锁单不允许删除");
        }
        //对物料库存进行解冻
        List<RecordInventoryDetail> recordInventoryDetails = recordInventoryDetailMapper.qryInventoryDetailByCode(recordInventoryInfo.getBoundIndex());
        for (RecordInventoryDetail recordInventoryDetail : recordInventoryDetails){
            //更新批次库存
            BasicMaterialBatchInventory materialBatchInventory = basicMaterialBatchInventoryService.getMaterialBatchInventory(recordInventoryDetail.getContainerCode(), recordInventoryDetail.getBatch(), recordInventoryDetail.getMaterialCode(), recordInventoryDetail.getProduceDate());
            materialBatchInventory.setMaterialNum(recordInventoryDetail.getInventoryNum());
            materialBatchInventory.setAvailNum(recordInventoryDetail.getMaterialNum());
            materialBatchInventory.setFreezeNum(0);
            basicMaterialBatchInventoryService.updateByPrimaryKeySelective(materialBatchInventory);
        }

        //根据单据编码删除盘点单据详情
        recordInventoryDetailMapper.deleteRecordInventoryDetail(recordInventoryInfo.getBoundIndex());
        recordInventoryInfoMapper.deleteById(recordInventoryInfo.getId());
        return ResponseResult.getSuccessResult();
    }

    /**
     *删除库存盘点详情
     */
    @Transactional
    public ResponseResult deleteRecordInventoryDetail(RecordInventoryDetail recordInventoryDetail) {
        RecordInventoryDetail inventoryDetail = recordInventoryDetailMapper.selectById(recordInventoryDetail.getId());
        recordInventoryDetailMapper.deleteById(inventoryDetail.getId());
        //更新总数
        RecordInventoryInfo recordInventoryInfo = recordInventoryInfoMapper.queryByBoundIndex(inventoryDetail.getBoundIndex());
        recordInventoryInfo.setTotalNum(recordInventoryInfo.getTotalNum() - inventoryDetail.getMaterialNum());
        recordInventoryInfoMapper.updateById(recordInventoryInfo);
        //解冻批次库存
        BasicMaterialBatchInventory materialBatchInventory = basicMaterialBatchInventoryService.getMaterialBatchInventory(inventoryDetail.getContainerCode(), inventoryDetail.getBatch(), inventoryDetail.getMaterialCode(), inventoryDetail.getProduceDate());
        materialBatchInventory.setAvailNum(inventoryDetail.getMaterialNum());
        materialBatchInventory.setFreezeNum(0);
        return ResponseResult.getSuccessResult();
    }

    /**
     *新增库存盘点详情
     */
    @Transactional
    public ResponseResult addRecordInventoryDetail(RecordInventoryDetail param) {
        if (param.getFreezeNum() != 0){
            return ResponseResult.getErrorResult("物料存在冻结物料，不允许发起盘点操作");
        }
        //更新盘点单总数
        RecordInventoryInfo recordInventoryInfo = recordInventoryInfoMapper.queryByBoundIndex(param.getBoundIndex());
        if (recordInventoryInfo == null){
            return ResponseResult.getErrorResult("盘点单不存在，请选择相应的盘点单");
        }
        recordInventoryInfo.setTotalNum(recordInventoryInfo.getTotalNum() + param.getMaterialNum());
        recordInventoryInfoMapper.updateById(recordInventoryInfo);
        //保存详情
        param.setId(UUID.randomUUID().toString());
        param.setRecordDate(new Date());
        recordInventoryDetailMapper.insert(param);
        //冻结批次库存
        BasicMaterialBatchInventory materialBatchInventory = basicMaterialBatchInventoryService.getMaterialBatchInventory(param.getContainerCode(), param.getBatch(), param.getMaterialCode(), param.getProduceDate());
        materialBatchInventory.setAvailNum(0);
        materialBatchInventory.setFreezeNum(param.getMaterialNum());
        basicMaterialBatchInventoryService.updateByPrimaryKeySelective(materialBatchInventory);
        return ResponseResult.getSuccessResult();
    }
}
