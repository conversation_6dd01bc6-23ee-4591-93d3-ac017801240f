package com.ruoyi.service.basicData;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.bean.BeanUtils;
import com.ruoyi.domain.basicData.BasicWarehouseContainer;
import com.ruoyi.domain.basicData.BasicWarehouseLocation;
import com.ruoyi.mapper.basicData.BasicWarehouseLocationMapper;
import com.ruoyi.utils.*;
import com.ruoyi.utils.constant.CommonConstant;
import com.ruoyi.vo.warehouse.BasicWarehouseLocationDto;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

@Service
public class BasicWarehouseLocationService extends ServiceImpl<BasicWarehouseLocationMapper, BasicWarehouseLocation> {

    @Resource
    BasicWarehouseLocationMapper basicWarehouseLocationMapper;
    @Resource
    BasicWarehouseContainerService basicWarehouseContainerService;
    @Resource
    BasicMaterialInfoService basicMaterialInfoService;
    /**
     * 新增库位信息
     */
    public ResponseResult addWarehouseLocation(BasicWarehouseLocation basicWarehouseLocation) {
        if (this.checkLocationNameUnique(basicWarehouseLocation)) {
            BasicWarehouseLocation parentLocation = basicWarehouseLocationMapper.selectById(basicWarehouseLocation.getParentId());
            if (parentLocation == null) {
                basicWarehouseLocation.setAncestors("0");
            } else {
                basicWarehouseLocation.setLocationType(parentLocation.getLocationType());
                basicWarehouseLocation.setAncestors(parentLocation.getAncestors() + "," + basicWarehouseLocation.getParentId());
            }
            basicWarehouseLocation.setLocationCode(this.getMaxIndex(CommonConstant.CodePrefix.LOCATION_PREFIX));
            basicWarehouseLocation.setId(UUID.randomUUID().toString());
            basicWarehouseLocation.setState(CommonConstant.LocationState.USE);
            this.save(basicWarehouseLocation);
            if (! basicWarehouseLocation.getNodeName().contains("货架")){
                //增加默认的容器
                BasicWarehouseContainer basicWarehouseContainer = new BasicWarehouseContainer();
                if(parentLocation != null && parentLocation.getLocationType() != null){
                    basicWarehouseContainer.setContainerType(parentLocation.getLocationType());
                }
                basicWarehouseContainer.setLocationCode(basicWarehouseLocation.getLocationCode());
                basicWarehouseContainer.setStatus(CommonConstant.ContainerState.USE);
                basicWarehouseContainer.setContainerHigh(0f);
                basicWarehouseContainer.setContainerWidth(0f);
                basicWarehouseContainer.setContainerLength(0f);
                basicWarehouseContainer.setRemark("默认增加的容器");
                basicWarehouseContainerService.addWarehouseContainer(basicWarehouseContainer);
            }
        } else {
            return ResponseResult.getErrorResult("新增'" + basicWarehouseLocation.getNodeName() + "'失败，名称已存在");
        }
        return ResponseResult.getSuccessResult();
    }

    /**
     * 复制仓库库位信息
     */
    @Transactional
    public ResponseResult copyWarehouseLocation(BasicWarehouseLocationDto param) {
        if (param == null) {
            return ResponseResult.getErrorResult("复制列表不能为空");
        }
        try {
            // 递归复制方法
            copyLocation(param, param.getParentId());
            return ResponseResult.getSuccessResult();
        } catch (Exception e) {
            // 记录异常信息并返回错误结果
            return ResponseResult.getErrorResult("复制过程中出现错误");
        }
    }

    /**
     * 递归处理子节点
     */
    private void copyLocation(BasicWarehouseLocationDto dto, String parentId) {
        BasicWarehouseLocation location = new BasicWarehouseLocation();
        BeanUtils.copyProperties(dto, location);
        location.setParentId(parentId);
        ResponseResult result = this.addWarehouseLocation(location);
        if (result.getCode().equals(ResultMsg.errorCode)) {
            throw new RuntimeException("复制失败");
        }
        // 查询父节点的 ID
        BasicWarehouseLocation savedLocation = basicWarehouseLocationMapper.checkLocationNameUnique(location);
        String newParentId = savedLocation.getId();
        // 处理子节点
        List<BasicWarehouseLocationDto> children = dto.getChildren();
        if (children != null && children.size()>0) {
            for (BasicWarehouseLocationDto child : children) {
                copyLocation(child, newParentId);
            }
        }
    }

    public String getMaxIndex(String str) {
        AutoNum an = new AutoNum();
        String strDate = an.getStrDate();
        String mxstr = this.basicWarehouseLocationMapper.getMaxIndex(str + strDate + "%");
        if (StringUtils.isEmpty(mxstr)) {
            mxstr = str + DateAndTimeUtil.getNowTimeNoSeparator();
        }
        return an.getNum(str, mxstr);
    }

    /**
     * 判断父节点下是否名称已存在
     */
    public boolean checkLocationNameUnique(BasicWarehouseLocation basicWarehouseLocation)
    {
        BasicWarehouseLocation info = basicWarehouseLocationMapper.checkLocationNameUnique(basicWarehouseLocation);
        if (StringUtils.isNotNull(info))
        {
            return false;
        }
        return true;



    }

    /**
     * 修改子元素关系
     *
     * @param id 被修改的ID
     * @param newAncestors 新的父ID集合
     * @param oldAncestors 旧的父ID集合
     */
    public void updateLocationChildren(String id, String newAncestors, String oldAncestors)
    {
        List<BasicWarehouseLocation> children = basicWarehouseLocationMapper.selectChildrenLocationById(id);
        for (BasicWarehouseLocation basicWarehouseLocation : children)
        {
            basicWarehouseLocation.setAncestors(basicWarehouseLocation.getAncestors().replaceFirst(oldAncestors, newAncestors));
        }
        if (children.size() > 0)
        {
            basicWarehouseLocationMapper.updateLocationChildren(children);
        }
    }

    /**
     * 更新库位信息
     */
    @Transactional
    public ResponseResult updateWarehouseLocation(BasicWarehouseLocation basicWarehouseLocation) {
        if (basicWarehouseLocation.getParentId() == basicWarehouseLocation.getId()) {
            return ResponseResult.getErrorResult("修改'" + basicWarehouseLocation.getNodeName() + "'失败，上级不能是自己");
        }
        if (!this.checkLocationNameUnique(basicWarehouseLocation)) {
            return ResponseResult.getErrorResult("修改'" + basicWarehouseLocation.getNodeName() + "'失败，名称已存在");
        }
        //更新祖级列表
        BasicWarehouseLocation newParent = basicWarehouseLocationMapper.selectById(basicWarehouseLocation.getParentId());
        BasicWarehouseLocation oldParent = basicWarehouseLocationMapper.selectById(basicWarehouseLocation.getId());
        if (oldParent.getState() != basicWarehouseLocation.getState()){
            if (basicWarehouseLocation.getState().equals(CommonConstant.LocationState.UN_USE)){
                //查询是否有容器
                List<BasicWarehouseContainer> basicWarehouseContainers = basicWarehouseContainerService.qryContainerByLocationCode(basicWarehouseLocation.getLocationCode());
                if (basicWarehouseContainers !=null && basicWarehouseContainers.size()>0){
                    return ResponseResult.getErrorResult("库位存在容器不允许对其进行禁用");
                }
            }
        }
        if (StringUtils.isNotNull(newParent) && StringUtils.isNotNull(oldParent)) {
            String newAncestors = newParent.getAncestors() + "," + newParent.getId();
            String oldAncestors = oldParent.getAncestors();
            basicWarehouseLocation.setAncestors(newAncestors);
            updateLocationChildren(basicWarehouseLocation.getId(), newAncestors, oldAncestors);
        }
        this.updateById(basicWarehouseLocation);
        return ResponseResult.getSuccessResult();
    }
    /**
     * 删除库位信息
     */
    @Transactional
    public ResponseResult deleteWarehouseLocation(List<BasicWarehouseLocation> basicWarehouseLocations) {
        for (BasicWarehouseLocation basicWarehouseLocation : basicWarehouseLocations){
            //判断是否有子节点
            if (this.hasChildById(basicWarehouseLocation.getId()))
            {
                return ResponseResult.getErrorResult("存在下级节点,不允许删除");
            }
            BasicWarehouseLocation basicWarehouseLocation1 = basicWarehouseLocationMapper.selectById(basicWarehouseLocation.getId());
            //判断是否有容器
            List<BasicWarehouseContainer> basicWarehouseContainers = basicWarehouseLocationMapper.qryMaterialContainer(basicWarehouseLocation1.getLocationCode());
            if (basicWarehouseContainers != null && basicWarehouseContainers.size()>0){
                return ResponseResult.getErrorResult(basicWarehouseLocation.getNodeName() + "库位存在容器内有物料,不允许进行删除操作");
            }else {
                basicWarehouseLocationMapper.deleteByLocationCode(basicWarehouseLocation1.getLocationCode());
            }
            this.removeById(basicWarehouseLocation.getId());
        }
        return ResponseResult.getSuccessResult();
    }

    /**
     * 是否存在子节点
     */
    public boolean hasChildById(String id)
    {
        int result = basicWarehouseLocationMapper.hasChildById(id);
        return result > 0;
    }
    /**
     * 查询库位信息
     */
    public List<BasicWarehouseLocation> queryWarehouseLocation(QueryParamVO queryParamVO) {
        QueryWrapper<BasicWarehouseLocation> queryWrapper = new QueryWrapper<>();
        // 添加动态条件
        if (queryParamVO.getKeyWord() != null && !queryParamVO.getKeyWord().isEmpty()) {
            queryWrapper.lambda()
                    .eq(BasicWarehouseLocation::getWarehouseCode, queryParamVO.getKeyWord());
        }
        if (queryParamVO.getState() != null) {
            queryWrapper.lambda()
                    .eq(BasicWarehouseLocation::getState, queryParamVO.getState());
        }
        return this.list(queryWrapper);
    }

    /**
     * 根据包含该id的祖级列表下最后一级库位
     */
    public List<BasicWarehouseLocation> queryLocationById(String id) {
        if (id == null) {
            return new ArrayList<>();
        }
        QueryWrapper<BasicWarehouseLocation> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .like(BasicWarehouseLocation::getAncestors, id)
                .apply("LENGTH(ancestors) - LENGTH(REPLACE(ancestors, ',', '')) > 1");
        List<BasicWarehouseLocation> list = basicWarehouseLocationMapper.selectList(queryWrapper);
        if (list.size() == 0){
            QueryWrapper<BasicWarehouseLocation> queryWrapper2 = new QueryWrapper<>();
            queryWrapper2.lambda().like(BasicWarehouseLocation::getId, id);
            return basicWarehouseLocationMapper.selectList(queryWrapper2);
        }else {
            return list;
        }
    }

    /**
     * 根据仓库编码查询最后一级库位
     */
    public List<BasicWarehouseLocation> queryLocationByWarehouseCode(String warehouseCode) {
        if (warehouseCode == null) {
            return new ArrayList<>();
        }
        QueryWrapper<BasicWarehouseLocation> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .eq(BasicWarehouseLocation::getWarehouseCode, warehouseCode)
                .apply("LENGTH(ancestors) - LENGTH(REPLACE(ancestors, ',', '')) > 1");
        return basicWarehouseLocationMapper.selectList(queryWrapper);
    }

    /**
     * 通过编码删除仓库库位信息
     */
    public ResponseResult deleteWarehouseLocationByCode(String warehouse_code) {
        QueryWrapper<BasicWarehouseLocation> qryWrapper = new QueryWrapper<>();
        qryWrapper.eq("warehouse_code", warehouse_code);
        List<BasicWarehouseLocation> list = this.list(qryWrapper);
        for (BasicWarehouseLocation basicWarehouseLocation : list){
            //判断是否有容器
            List<BasicWarehouseContainer> basicWarehouseContainers = basicWarehouseLocationMapper.qryMaterialContainer(basicWarehouseLocation.getLocationCode());
            if (basicWarehouseContainers != null && basicWarehouseContainers.size()>0){
                return ResponseResult.getErrorResult(basicWarehouseLocation.getNodeName() + "库位存在容器内有物料,不允许进行删除操作");
            }else {
                basicWarehouseLocationMapper.deleteByLocationCode(basicWarehouseLocation.getLocationCode());
            }
            this.removeById(basicWarehouseLocation.getId());
        }
        return ResponseResult.getSuccessResult();
    }

    public BasicWarehouseLocation selectByPrimaryKey(String id) {
        BasicWarehouseLocation basicWarehouseLocation = basicWarehouseLocationMapper.selectById(id);
        return basicWarehouseLocation;
    }
}
